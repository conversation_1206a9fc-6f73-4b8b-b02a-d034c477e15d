<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title( '|', true, 'right' ); ?></title>
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home">
                    <img src="<?php echo get_template_directory_uri(); ?>/Logo.png" alt="<?php bloginfo( 'name' ); ?>" class="site-logo">
                </a>
            </div>
            <div class="search-form-container">
                <?php get_search_form(); ?>
            </div>
            <div class="header-right">
                <?php if ( is_user_logged_in() ) : ?>
                    <div class="education-panel">
                        <a href="#" class="education-panel-button" id="education-panel-button">
                            <div class="education-panel-icon">
                                <svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="education-panel-text">
                                <span class="education-panel-main">Egitim Paneli</span>
                                <span class="education-panel-sub">Kurslari Izlemek icin</span>
                            </div>
                        </a>
                    </div>
                <?php endif; ?>
                <div class="user-actions">
                    <?php if ( is_user_logged_in() ) : ?>
                        <?php
                        $current_user = wp_get_current_user();
                        $user_avatar_url = get_avatar_url( $current_user->ID, array( 'size' => 40 ) );
                        ?>
                        <div class="user-profile-dropdown">
                            <button class="user-avatar-button" id="user-avatar-toggle" aria-expanded="false" aria-haspopup="true">
                                <div class="user-avatar-circle">
                                    <img src="<?php echo esc_url( $user_avatar_url ); ?>" alt="<?php echo esc_attr( $current_user->display_name ); ?>" class="user-avatar-img">
                                </div>
                            </button>
                            <div class="user-dropdown-menu" id="user-dropdown-menu" role="menu">
                                <div class="user-info">
                                    <span class="user-name"><?php echo esc_html( $current_user->display_name ); ?></span>
                                    <span class="user-email"><?php echo esc_html( $current_user->user_email ); ?></span>
                                </div>
                                <div class="user-menu-items">
                                    <?php
                                    // WooCommerce hesabim sayfasi menu ogelerini al
                                    if ( function_exists( 'wc_get_account_menu_items' ) ) {
                                        $menu_items = wc_get_account_menu_items();
                                        $myaccount_page_id = get_option( 'woocommerce_myaccount_page_id' );
                                        $myaccount_page_url = $myaccount_page_id ? get_permalink( $myaccount_page_id ) : '';

                                        foreach ( $menu_items as $endpoint => $label ) {
                                            // Oturumu kapat menusunu gizle
                                            if ( $endpoint === 'customer-logout' ) {
                                                continue;
                                            }

                                            $url = wc_get_account_endpoint_url( $endpoint );
                                            $icon_svg = '';

                                            // Debug: endpoint adlarini kontrol et (gelistirme icin)
                                            echo '<!-- Endpoint: ' . $endpoint . ' - Label: ' . $label . ' -->';

                                            // Her menu ogesi icin uygun ikon belirle (WooCommerce sidebar ile ayni iconlar)
                                            switch ( $endpoint ) {
                                                case 'dashboard':
                                                    // Dashboard iconu - WooCommerce sidebar ile ayni
                                                    $icon_svg = '<rect width="16" height="16" x="4" y="4" stroke="currentColor" stroke-linecap="round" stroke-width="1.5" rx="2"/><path d="M4 9h16M9 10v10" stroke="currentColor" stroke-linecap="round" stroke-width="1.5"/>';
                                                    break;
                                                case 'orders':
                                                    // Siparisler iconu - WooCommerce sidebar ile ayni
                                                    $icon_svg = '<path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                case 'downloads':
                                                    // Indirmeler iconu - WooCommerce sidebar ile ayni
                                                    $icon_svg = '<path d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 12L12 16M12 16L8 12M12 16L12 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                case 'edit-address':
                                                    // Adres iconu - WooCommerce sidebar ile ayni
                                                    $icon_svg = '<path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                case 'payment-methods':
                                                    // Odeme yontemleri iconu - WooCommerce sidebar ile ayni
                                                    $icon_svg = '<path d="M3 10H21M7 15H8M12 15H13M6 19H18C19.6569 19 21 17.6569 21 16V8C21 6.34315 19.6569 5 18 5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                case 'edit-account':
                                                    // Hesap duzenle iconu - WooCommerce sidebar ile ayni
                                                    $icon_svg = '<path d="M5.12104 17.8037C7.15267 16.6554 9.4998 16 12 16C14.5002 16 16.8473 16.6554 18.879 17.8037M15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10ZM21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                case 'wishlist':
                                                    // Istek listesi iconu - WooCommerce sidebar ile ayni (kalp)
                                                    $icon_svg = '<path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 3C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 3C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.041 1.55 8.5C1.5487 9.959 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.45 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                case 'reviews':
                                                case 'customer-reviews':
                                                case 'degerlendirmeler':
                                                case 'review':
                                                case 'my-reviews':
                                                case 'user-reviews':
                                                    // Degerlendirme iconu - WooCommerce sidebar ile ayni (yildiz)
                                                    $icon_svg = '<polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26 12,2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                                default:
                                                    // Varsayilan ikon
                                                    $icon_svg = '<circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>';
                                                    break;
                                            }

                                            echo '<a href="' . esc_url( $url ) . '" class="user-menu-item" role="menuitem">';
                                            echo '<svg class="menu-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">';
                                            echo $icon_svg;
                                            echo '</svg>';
                                            echo esc_html( $label );
                                            echo '</a>';
                                        }
                                    } else {
                                        // WooCommerce aktif degilse sadece hesabim linkini goster
                                        ?>
                                        <a href="<?php echo esc_url( get_permalink( get_option('woocommerce_myaccount_page_id') ) ); ?>" class="user-menu-item" role="menuitem">
                                            <svg class="menu-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Hesabim
                                        </a>
                                        <?php
                                    }
                                    ?>
                                    <a href="<?php echo esc_url( wp_logout_url( home_url() ) ); ?>" class="user-menu-item logout" role="menuitem">
                                        <svg class="menu-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Oturumu kapat
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else : ?>
                        <a href="<?php echo esc_url( get_permalink( get_option('woocommerce_myaccount_page_id') ) ); ?>" class="login-button">
                            <svg class="login-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="10,17 15,12 10,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="15" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="login-text">
                                <span class="login-main">Giris Yap</span>
                                <span class="login-sub">veya uye ol</span>
                            </span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="header-bottom">
        <div class="container">
            <nav class="main-navigation">
                <?php
                wp_nav_menu( array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'walker'         => new Dmr_Walker_Nav_Menu(),
                ) );
                ?>
            </nav>
            <!-- Yeni Demir Stili Sepet Butonu -->
            <div class="site-header-cart">
                <nav class="demir-cart" aria-label="Sepet icerigi">
                    <a class="cart-contents"
                       role="button"
                       href="#"
                       id="cart-toggle"
                       title="Sepetinizi goruntuleyiniz">

                        <span class="amount"><?php
                            if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
                                echo wp_kses_post( WC()->cart->get_cart_subtotal() );
                            } else {
                                echo '₺0,00';
                            }
                        ?></span>

                        <span class="demir-cart-icon">
                            <svg aria-hidden="true" role="presentation" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            <span class="cart-item-count"><?php
                                if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
                                    echo WC()->cart->get_cart_contents_count();
                                } else {
                                    echo '0';
                                }
                            ?></span>
                        </span>
                    </a>
                </nav>
            </div>
        </div>
    </div>
</header>

<!-- Sepet Sidebar Overlay -->
<div class="cart-sidebar-overlay" id="cart-sidebar-overlay"></div>

<!-- Sepet Sidebar -->
<div id="cart-sidebar" class="cart-sidebar">
    <div class="cart-sidebar-content">
        <div class="cart-sidebar-header">
            <h3>Sepetim</h3>
            <button class="cart-sidebar-close" id="cart-sidebar-close">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
        <div class="cart-sidebar-body">
            <?php if ( class_exists( 'WooCommerce' ) ) : ?>
                <div class="widget_shopping_cart_content">
                    <?php woocommerce_mini_cart(); ?>
                </div>
            <?php else : ?>
                <p>WooCommerce eklentisi aktif degil.</p>
            <?php endif; ?>
        </div>
        <div class="cart-sidebar-footer">
            <div class="cart-footer-info">
                <p><small>Ucretsiz kargo 500 TL ve uzeri alisverislerde</small></p>
            </div>
        </div>
    </div>
</div>
